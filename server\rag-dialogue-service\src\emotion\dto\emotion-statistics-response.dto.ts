import { ApiProperty } from '@nestjs/swagger';

export class EmotionStatisticsDto {
  @ApiProperty({ description: '支持的情感类型列表', type: [String] })
  supportedEmotions: string[];

  @ApiProperty({ description: '情感词典大小' })
  dictionarySize: number;

  @ApiProperty({ description: '否定词数量' })
  negationWordsCount: number;
}

export class EmotionStatisticsResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '情感统计信息', type: EmotionStatisticsDto })
  data: EmotionStatisticsDto;

  @ApiProperty({ description: '响应消息' })
  message: string;
}
