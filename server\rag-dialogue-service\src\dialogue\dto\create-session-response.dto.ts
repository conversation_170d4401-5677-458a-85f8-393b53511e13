import { ApiProperty } from '@nestjs/swagger';

export class CreateSessionDataDto {
  @ApiProperty({ description: '会话ID' })
  sessionId: string;

  @ApiProperty({ description: '场景ID' })
  sceneId: string;

  @ApiProperty({ description: '数字人ID' })
  avatarId: string;

  @ApiProperty({ description: '知识库ID' })
  knowledgeBaseId: string;

  @ApiProperty({ description: '会话状态', enum: ['active', 'paused', 'ended'] })
  status: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;
}

export class CreateSessionResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '会话数据', type: CreateSessionDataDto })
  data: CreateSessionDataDto;

  @ApiProperty({ description: '响应消息' })
  message: string;
}
