import { ApiProperty } from '@nestjs/swagger';
import { IntentResultDto } from './intent-analysis-response.dto';

export class BatchIntentAnalysisDataDto {
  @ApiProperty({ description: '意图分析结果列表', type: [IntentResultDto] })
  results: IntentResultDto[];

  @ApiProperty({ description: '总数量' })
  total: number;
}

export class BatchIntentAnalysisResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '批量意图分析结果', type: BatchIntentAnalysisDataDto })
  data: BatchIntentAnalysisDataDto;

  @ApiProperty({ description: '响应消息' })
  message: string;
}
