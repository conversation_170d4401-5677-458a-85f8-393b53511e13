import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { DialogueSession } from './dialogue-session.entity';

@Entity('dialogue_messages')
@Index(['sessionId', 'createdAt'])
@Index(['role', 'type'])
export class DialogueMessage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'session_id', length: 100 })
  @Index()
  sessionId: string;

  @Column({
    type: 'enum',
    enum: ['user', 'assistant', 'system'],
  })
  role: 'user' | 'assistant' | 'system';

  @Column({ type: 'text' })
  content: string;

  @Column({
    type: 'enum',
    enum: ['text', 'audio', 'image', 'file'],
    default: 'text',
  })
  type: 'text' | 'audio' | 'image' | 'file';

  @Column({ name: 'audio_data', type: 'longtext', nullable: true })
  audioData?: string;

  @Column({ name: 'image_url', length: 500, nullable: true })
  imageUrl?: string;

  @Column({ name: 'file_url', length: 500, nullable: true })
  fileUrl?: string;

  @Column({ type: 'json', nullable: true })
  metadata?: {
    intent?: {
      type: string;
      confidence: number;
      entities?: any[];
    };
    emotion?: {
      type: string;
      intensity: number;
      confidence?: number;
    };
    sources?: Array<{
      id: string;
      content: string;
      score: number;
    }>;
    confidence?: number;
    responseTime?: number;
    tokens?: number;
    error?: string;
    customData?: Record<string, any>;
  };

  @Column({ name: 'token_count', type: 'int', default: 0 })
  tokenCount: number;

  @Column({ name: 'response_time', type: 'int', nullable: true })
  responseTime?: number;

  @Column({ name: 'confidence_score', type: 'float', nullable: true })
  confidenceScore?: number;

  @Column({ name: 'feedback_score', type: 'int', nullable: true })
  feedbackScore?: number; // 1-5 用户反馈评分

  @Column({ name: 'feedback_comment', type: 'text', nullable: true })
  feedbackComment?: string;

  @Column({ name: 'is_edited', type: 'boolean', default: false })
  isEdited: boolean;

  @Column({ name: 'edited_at', type: 'timestamp', nullable: true })
  editedAt?: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => DialogueSession, session => session.messages, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'session_id' })
  session: DialogueSession;

  // 虚拟属性
  get hasIntent(): boolean {
    return !!(this.metadata?.intent);
  }

  get hasEmotion(): boolean {
    return !!(this.metadata?.emotion);
  }

  get hasSources(): boolean {
    return !!(this.metadata?.sources && this.metadata.sources.length > 0);
  }

  get isUserMessage(): boolean {
    return this.role === 'user';
  }

  get isAssistantMessage(): boolean {
    return this.role === 'assistant';
  }

  get hasPositiveFeedback(): boolean {
    return this.feedbackScore ? this.feedbackScore >= 4 : false;
  }

  get hasNegativeFeedback(): boolean {
    return this.feedbackScore ? this.feedbackScore <= 2 : false;
  }
}
