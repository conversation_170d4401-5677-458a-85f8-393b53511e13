import { ApiProperty } from '@nestjs/swagger';

export class EmotionResultDto {
  @ApiProperty({ 
    description: '情感类型',
    enum: ['happy', 'sad', 'angry', 'surprised', 'fear', 'disgusted', 'neutral', 'excited', 'calm', 'confused'],
  })
  type: string;

  @ApiProperty({ description: '情感强度 (0-1)' })
  intensity: number;

  @ApiProperty({ description: '置信度 (0-1)' })
  confidence: number;

  @ApiProperty({ 
    description: '详细情感分数',
    example: {
      happy: 0.8,
      sad: 0.1,
      angry: 0.0,
      neutral: 0.1,
    },
  })
  details: Record<string, number>;
}

export class EmotionAnalysisResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '情感分析结果', type: EmotionResultDto })
  data: EmotionResultDto;

  @ApiProperty({ description: '响应消息' })
  message: string;
}
