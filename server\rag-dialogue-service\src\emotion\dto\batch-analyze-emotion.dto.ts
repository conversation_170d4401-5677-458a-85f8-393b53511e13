import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString, IsNotEmpty, MaxLength, ArrayMaxSize } from 'class-validator';

export class BatchAnalyzeEmotionDto {
  @ApiProperty({ 
    description: '要分析的文本列表',
    example: ['我很开心', '我很生气', '我感到困惑'],
    maxItems: 100,
  })
  @IsArray()
  @ArrayMaxSize(100)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  @MaxLength(1000, { each: true })
  texts: string[];
}
