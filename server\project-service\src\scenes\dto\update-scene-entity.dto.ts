/**
 * 更新场景实体DTO
 */
import { IsString, IsOptional, IsObject, ValidateNested, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

class TransformDto {
  @ApiProperty({ description: '位置', example: [0, 0, 0] })
  @IsArray()
  @IsOptional()
  position?: [number, number, number];

  @ApiProperty({ description: '旋转', example: [0, 0, 0] })
  @IsArray()
  @IsOptional()
  rotation?: [number, number, number];

  @ApiProperty({ description: '缩放', example: [1, 1, 1] })
  @IsArray()
  @IsOptional()
  scale?: [number, number, number];
}

export class UpdateSceneEntityDto {
  @ApiProperty({ description: '实体名称', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '实体类型', required: false })
  @IsString()
  @IsOptional()
  type?: string;

  @ApiProperty({ description: '变换', required: false, type: TransformDto })
  @ValidateNested()
  @Type(() => TransformDto)
  @IsOptional()
  transform?: TransformDto;

  @ApiProperty({ description: '实体属性', required: false, type: 'object' })
  @IsObject()
  @IsOptional()
  properties?: Record<string, any>;

  @ApiProperty({ description: '父实体ID', required: false })
  @IsString()
  @IsOptional()
  parentId?: string;
}
