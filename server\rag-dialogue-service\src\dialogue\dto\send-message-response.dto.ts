import { ApiProperty } from '@nestjs/swagger';

export class IntentDto {
  @ApiProperty({ description: '意图类型' })
  type: string;

  @ApiProperty({ description: '置信度' })
  confidence: number;

  @ApiProperty({ description: '实体列表', required: false })
  entities?: any[];
}

export class EmotionDto {
  @ApiProperty({ description: '情感类型' })
  type: string;

  @ApiProperty({ description: '情感强度' })
  intensity: number;
}

export class SourceDto {
  @ApiProperty({ description: '来源ID' })
  id: string;

  @ApiProperty({ description: '内容片段' })
  content: string;

  @ApiProperty({ description: '相关性分数' })
  score: number;
}

export class DialogueResponseDataDto {
  @ApiProperty({ description: '回答文本' })
  text: string;

  @ApiProperty({ description: '意图分析结果', type: IntentDto })
  intent: IntentDto;

  @ApiProperty({ description: '情感分析结果', type: EmotionDto })
  emotion: EmotionDto;

  @ApiProperty({ description: '知识来源', type: [SourceDto] })
  sources: SourceDto[];

  @ApiProperty({ description: '回答置信度' })
  confidence: number;

  @ApiProperty({ description: '响应时间(毫秒)' })
  responseTime: number;
}

export class SendMessageResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '对话响应数据', type: DialogueResponseDataDto })
  data: DialogueResponseDataDto;

  @ApiProperty({ description: '响应消息' })
  message: string;
}
