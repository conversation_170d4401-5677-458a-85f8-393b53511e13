import { ApiProperty } from '@nestjs/swagger';

export class SessionSummaryDto {
  @ApiProperty({ description: '会话ID' })
  id: string;

  @ApiProperty({ description: '场景ID' })
  sceneId: string;

  @ApiProperty({ description: '数字人ID' })
  avatarId: string;

  @ApiProperty({ description: '知识库ID' })
  knowledgeBaseId: string;

  @ApiProperty({ description: '用户ID', required: false })
  userId?: string;

  @ApiProperty({ description: '会话状态', enum: ['active', 'paused', 'ended'] })
  status: string;

  @ApiProperty({ description: '消息数量' })
  messageCount: number;

  @ApiProperty({ description: '平均响应时间' })
  avgResponseTime: number;

  @ApiProperty({ description: '满意度评分', required: false })
  satisfactionScore?: number;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ description: '结束时间', required: false })
  endedAt?: Date;
}

export class SessionListDataDto {
  @ApiProperty({ description: '会话列表', type: [SessionSummaryDto] })
  data: SessionSummaryDto[];

  @ApiProperty({ description: '总数量' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;
}

export class SessionListResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '会话列表数据', type: SessionListDataDto })
  data: SessionListDataDto;

  @ApiProperty({ description: '响应消息' })
  message: string;
}
