import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString, IsNotEmpty, MaxLength, ArrayMaxSize } from 'class-validator';

export class BatchAnalyzeIntentDto {
  @ApiProperty({ 
    description: '要分析的文本列表',
    example: ['你好', '我想投诉', '谢谢你的帮助'],
    maxItems: 100,
  })
  @IsArray()
  @ArrayMaxSize(100)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  @MaxLength(1000, { each: true })
  texts: string[];
}
