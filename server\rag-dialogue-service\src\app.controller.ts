import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('app')
@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: '获取服务信息' })
  @ApiResponse({ 
    status: 200, 
    description: '服务信息',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        version: { type: 'string' },
        description: { type: 'string' },
        status: { type: 'string' },
        timestamp: { type: 'string' },
      },
    },
  })
  getServiceInfo() {
    this.logger.log('获取服务信息');
    return this.appService.getServiceInfo();
  }

  @Get('version')
  @ApiOperation({ summary: '获取服务版本' })
  @ApiResponse({ 
    status: 200, 
    description: '服务版本信息',
    schema: {
      type: 'object',
      properties: {
        version: { type: 'string' },
        buildTime: { type: 'string' },
        gitCommit: { type: 'string' },
      },
    },
  })
  getVersion() {
    this.logger.log('获取服务版本');
    return this.appService.getVersion();
  }
}
