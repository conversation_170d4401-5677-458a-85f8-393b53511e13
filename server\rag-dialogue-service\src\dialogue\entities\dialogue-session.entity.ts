import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { DialogueMessage } from './dialogue-message.entity';

@Entity('dialogue_sessions')
@Index(['userId', 'status'])
@Index(['sceneId', 'avatarId'])
@Index(['knowledgeBaseId'])
export class DialogueSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'scene_id', length: 100 })
  @Index()
  sceneId: string;

  @Column({ name: 'avatar_id', length: 100 })
  @Index()
  avatarId: string;

  @Column({ name: 'knowledge_base_id', length: 100 })
  @Index()
  knowledgeBaseId: string;

  @Column({ name: 'user_id', length: 100, nullable: true })
  @Index()
  userId?: string;

  @Column({
    type: 'enum',
    enum: ['active', 'paused', 'ended'],
    default: 'active',
  })
  @Index()
  status: 'active' | 'paused' | 'ended';

  @Column({ type: 'json', nullable: true })
  metadata?: {
    personality?: string;
    responseStyle?: string;
    language?: string;
    customSettings?: Record<string, any>;
    createdAt?: string;
    lastActivity?: string;
  };

  @Column({ name: 'message_count', type: 'int', default: 0 })
  messageCount: number;

  @Column({ name: 'total_tokens', type: 'int', default: 0 })
  totalTokens: number;

  @Column({ name: 'avg_response_time', type: 'float', default: 0 })
  avgResponseTime: number;

  @Column({ name: 'satisfaction_score', type: 'float', nullable: true })
  satisfactionScore?: number;

  @Column({ name: 'ended_at', type: 'timestamp', nullable: true })
  endedAt?: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => DialogueMessage, message => message.session, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  messages: DialogueMessage[];

  // 虚拟属性
  get duration(): number {
    if (this.endedAt) {
      return this.endedAt.getTime() - this.createdAt.getTime();
    }
    return Date.now() - this.createdAt.getTime();
  }

  get isActive(): boolean {
    return this.status === 'active';
  }

  get lastActivity(): Date {
    return this.metadata?.lastActivity 
      ? new Date(this.metadata.lastActivity)
      : this.updatedAt;
  }
}
