import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 获取服务健康状态
   */
  getHealthStatus() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'rag-dialogue-service',
      version: '1.0.0',
      uptime: process.uptime(),
      environment: this.configService.get<string>('NODE_ENV', 'development'),
    };
  }

  /**
   * 检查依赖服务状态
   */
  async checkDependencies() {
    const dependencies = {
      database: await this.checkDatabase(),
      redis: await this.checkRedis(),
      openai: await this.checkOpenAI(),
      knowledgeBase: await this.checkKnowledgeBase(),
    };

    return dependencies;
  }

  /**
   * 检查数据库连接
   */
  private async checkDatabase(): Promise<{ status: string; message?: string }> {
    try {
      // 这里应该实际检查数据库连接
      return { status: 'healthy' };
    } catch (error) {
      this.logger.error('数据库连接检查失败:', error);
      return { status: 'unhealthy', message: error.message };
    }
  }

  /**
   * 检查Redis连接
   */
  private async checkRedis(): Promise<{ status: string; message?: string }> {
    try {
      // 这里应该实际检查Redis连接
      return { status: 'healthy' };
    } catch (error) {
      this.logger.error('Redis连接检查失败:', error);
      return { status: 'unhealthy', message: error.message };
    }
  }

  /**
   * 检查OpenAI API
   */
  private async checkOpenAI(): Promise<{ status: string; message?: string }> {
    try {
      const apiKey = this.configService.get<string>('OPENAI_API_KEY');
      if (!apiKey) {
        return { status: 'unhealthy', message: 'OpenAI API密钥未配置' };
      }
      return { status: 'healthy' };
    } catch (error) {
      this.logger.error('OpenAI API检查失败:', error);
      return { status: 'unhealthy', message: error.message };
    }
  }

  /**
   * 检查知识库服务
   */
  private async checkKnowledgeBase(): Promise<{ status: string; message?: string }> {
    try {
      const serviceUrl = this.configService.get<string>('KNOWLEDGE_BASE_SERVICE_URL');
      if (!serviceUrl) {
        return { status: 'unhealthy', message: '知识库服务URL未配置' };
      }
      return { status: 'healthy' };
    } catch (error) {
      this.logger.error('知识库服务检查失败:', error);
      return { status: 'unhealthy', message: error.message };
    }
  }

  /**
   * 获取系统资源使用情况
   */
  getSystemResources() {
    return {
      memory: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      uptime: process.uptime(),
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      pid: process.pid,
    };
  }
}
