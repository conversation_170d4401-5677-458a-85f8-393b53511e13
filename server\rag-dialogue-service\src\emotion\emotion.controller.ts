import {
  Controller,
  Post,
  Get,
  Body,
  HttpStatus,
  HttpException,
  Logger,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
} from '@nestjs/swagger';
import { EmotionService, EmotionResult } from './emotion.service';
import { AnalyzeEmotionDto } from './dto/analyze-emotion.dto';
import { BatchAnalyzeEmotionDto } from './dto/batch-analyze-emotion.dto';
import { EmotionAnalysisResponseDto } from './dto/emotion-analysis-response.dto';
import { BatchEmotionAnalysisResponseDto } from './dto/batch-emotion-analysis-response.dto';
import { EmotionStatisticsResponseDto } from './dto/emotion-statistics-response.dto';

@ApiTags('emotion')
@Controller('emotion')
export class EmotionController {
  private readonly logger = new Logger(EmotionController.name);

  constructor(private readonly emotionService: EmotionService) {}

  @Post('analyze')
  @ApiOperation({ summary: '分析文本情感' })
  @ApiBody({ type: AnalyzeEmotionDto })
  @ApiResponse({
    status: 200,
    description: '情感分析成功',
    type: EmotionAnalysisResponseDto,
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async analyzeEmotion(
    @Body(ValidationPipe) analyzeDto: AnalyzeEmotionDto,
  ): Promise<EmotionAnalysisResponseDto> {
    try {
      this.logger.log(`分析情感: ${analyzeDto.text}`);
      
      const result = await this.emotionService.analyzeEmotion(analyzeDto.text);
      
      return {
        success: true,
        data: result,
        message: '情感分析成功',
      };
    } catch (error) {
      this.logger.error(`情感分析失败: ${error.message}`, error.stack);
      throw new HttpException(
        `情感分析失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('batch-analyze')
  @ApiOperation({ summary: '批量分析文本情感' })
  @ApiBody({ type: BatchAnalyzeEmotionDto })
  @ApiResponse({
    status: 200,
    description: '批量情感分析成功',
    type: BatchEmotionAnalysisResponseDto,
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async batchAnalyzeEmotion(
    @Body(ValidationPipe) batchDto: BatchAnalyzeEmotionDto,
  ): Promise<BatchEmotionAnalysisResponseDto> {
    try {
      this.logger.log(`批量分析情感: ${batchDto.texts.length} 条文本`);
      
      const results = await this.emotionService.batchAnalyzeEmotion(batchDto.texts);
      
      return {
        success: true,
        data: {
          results,
          total: results.length,
        },
        message: '批量情感分析成功',
      };
    } catch (error) {
      this.logger.error(`批量情感分析失败: ${error.message}`, error.stack);
      throw new HttpException(
        `批量情感分析失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取情感分析统计信息' })
  @ApiResponse({
    status: 200,
    description: '获取统计信息成功',
    type: EmotionStatisticsResponseDto,
  })
  async getStatistics(): Promise<EmotionStatisticsResponseDto> {
    try {
      this.logger.log('获取情感分析统计信息');
      
      const statistics = this.emotionService.getEmotionStatistics();
      
      return {
        success: true,
        data: statistics,
        message: '获取统计信息成功',
      };
    } catch (error) {
      this.logger.error(`获取统计信息失败: ${error.message}`, error.stack);
      throw new HttpException(
        `获取统计信息失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
