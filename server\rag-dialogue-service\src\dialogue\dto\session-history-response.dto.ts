import { ApiProperty } from '@nestjs/swagger';

export class MessageMetadataDto {
  @ApiProperty({ description: '意图信息', required: false })
  intent?: {
    type: string;
    confidence: number;
    entities?: any[];
  };

  @ApiProperty({ description: '情感信息', required: false })
  emotion?: {
    type: string;
    intensity: number;
    confidence?: number;
  };

  @ApiProperty({ description: '知识来源', required: false })
  sources?: Array<{
    id: string;
    content: string;
    score: number;
  }>;

  @ApiProperty({ description: '置信度', required: false })
  confidence?: number;

  @ApiProperty({ description: '响应时间', required: false })
  responseTime?: number;
}

export class DialogueMessageDto {
  @ApiProperty({ description: '消息ID' })
  id: string;

  @ApiProperty({ description: '会话ID' })
  sessionId: string;

  @ApiProperty({ description: '角色', enum: ['user', 'assistant', 'system'] })
  role: string;

  @ApiProperty({ description: '消息内容' })
  content: string;

  @ApiProperty({ description: '消息类型', enum: ['text', 'audio', 'image', 'file'] })
  type: string;

  @ApiProperty({ description: '元数据', type: MessageMetadataDto, required: false })
  metadata?: MessageMetadataDto;

  @ApiProperty({ description: 'Token数量' })
  tokenCount: number;

  @ApiProperty({ description: '响应时间', required: false })
  responseTime?: number;

  @ApiProperty({ description: '置信度分数', required: false })
  confidenceScore?: number;

  @ApiProperty({ description: '用户反馈分数', required: false })
  feedbackScore?: number;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;
}

export class SessionHistoryDataDto {
  @ApiProperty({ description: '消息列表', type: [DialogueMessageDto] })
  data: DialogueMessageDto[];

  @ApiProperty({ description: '总数量' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;
}

export class SessionHistoryResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '历史数据', type: SessionHistoryDataDto })
  data: SessionHistoryDataDto;

  @ApiProperty({ description: '响应消息' })
  message: string;
}
