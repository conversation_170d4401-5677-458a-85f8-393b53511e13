import { ApiProperty } from '@nestjs/swagger';

export class IntentStatisticsDto {
  @ApiProperty({ description: '支持的意图类型列表', type: [String] })
  supportedIntents: string[];

  @ApiProperty({ description: '意图模式数量' })
  patternCount: number;

  @ApiProperty({ description: '关键词总数' })
  totalKeywords: number;
}

export class IntentStatisticsResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '意图统计信息', type: IntentStatisticsDto })
  data: IntentStatisticsDto;

  @ApiProperty({ description: '响应消息' })
  message: string;
}
