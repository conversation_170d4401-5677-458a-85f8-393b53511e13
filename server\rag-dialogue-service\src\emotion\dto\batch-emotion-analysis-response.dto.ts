import { ApiProperty } from '@nestjs/swagger';
import { EmotionResultDto } from './emotion-analysis-response.dto';

export class BatchEmotionAnalysisDataDto {
  @ApiProperty({ description: '情感分析结果列表', type: [EmotionResultDto] })
  results: EmotionResultDto[];

  @ApiProperty({ description: '总数量' })
  total: number;
}

export class BatchEmotionAnalysisResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '批量情感分析结果', type: BatchEmotionAnalysisDataDto })
  data: BatchEmotionAnalysisDataDto;

  @ApiProperty({ description: '响应消息' })
  message: string;
}
