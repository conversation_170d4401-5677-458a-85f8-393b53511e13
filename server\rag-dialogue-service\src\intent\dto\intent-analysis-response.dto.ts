import { ApiProperty } from '@nestjs/swagger';

export class EntityDto {
  @ApiProperty({ description: '实体类型' })
  type: string;

  @ApiProperty({ description: '实体值' })
  value: string;

  @ApiProperty({ description: '开始位置' })
  start: number;

  @ApiProperty({ description: '结束位置' })
  end: number;

  @ApiProperty({ description: '置信度' })
  confidence: number;
}

export class IntentResultDto {
  @ApiProperty({ 
    description: '意图类型',
    enum: ['greeting', 'question', 'complaint', 'praise', 'request', 'goodbye', 'chitchat', 'unknown'],
  })
  type: string;

  @ApiProperty({ description: '置信度' })
  confidence: number;

  @ApiProperty({ description: '实体列表', type: [EntityDto] })
  entities: EntityDto[];

  @ApiProperty({ description: '关键词列表', type: [String] })
  keywords: string[];
}

export class IntentAnalysisResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '意图分析结果', type: IntentResultDto })
  data: IntentResultDto;

  @ApiProperty({ description: '响应消息' })
  message: string;
}
