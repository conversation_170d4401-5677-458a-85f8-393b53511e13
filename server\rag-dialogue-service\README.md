# RAG对话服务 (RAG Dialogue Service)

DL引擎的智能对话服务，提供基于RAG（检索增强生成）的对话功能，包括意图理解、情感分析、知识检索和智能回答生成。

## 🚀 功能特性

### 核心功能
- **智能对话**: 基于RAG技术的智能对话系统
- **意图理解**: 自然语言意图识别和分类
- **情感分析**: 文本情感识别和强度分析
- **知识检索**: 从知识库中检索相关信息
- **回答生成**: 基于上下文的智能回答生成
- **会话管理**: 完整的对话会话生命周期管理

### 技术特性
- **微服务架构**: 支持独立部署和扩展
- **实时通信**: WebSocket支持实时对话
- **多模态支持**: 文本、音频、图片等多种输入
- **缓存优化**: Redis缓存提升响应速度
- **队列处理**: 异步消息处理机制
- **健康监控**: 完整的健康检查和监控

## 📋 系统要求

- Node.js >= 16.0.0
- MySQL >= 8.0
- Redis >= 6.0
- OpenAI API Key

## 🛠️ 安装部署

### 1. 安装依赖
```bash
npm install
```

### 2. 环境配置
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库、Redis、OpenAI等信息
```

### 3. 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE rag_dialogue CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行迁移（开发环境会自动同步）
npm run start:dev
```

### 4. 启动服务
```bash
# 开发环境
npm run start:dev

# 生产环境
npm run build
npm run start:prod
```

## 📚 API文档

服务启动后，可通过以下地址访问API文档：
- Swagger文档: http://localhost:4009/api/docs
- 健康检查: http://localhost:4009/health

### 主要API端点

#### 对话管理
- `POST /api/v1/dialogue/sessions` - 创建对话会话
- `POST /api/v1/dialogue/sessions/:id/messages` - 发送消息
- `GET /api/v1/dialogue/sessions/:id/history` - 获取对话历史
- `DELETE /api/v1/dialogue/sessions/:id` - 结束会话

#### 意图分析
- `POST /api/v1/intent/analyze` - 分析文本意图
- `POST /api/v1/intent/batch-analyze` - 批量意图分析
- `GET /api/v1/intent/statistics` - 获取意图统计

#### 情感分析
- `POST /api/v1/emotion/analyze` - 分析文本情感
- `POST /api/v1/emotion/batch-analyze` - 批量情感分析
- `GET /api/v1/emotion/statistics` - 获取情感统计

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| NODE_ENV | 运行环境 | development |
| PORT | HTTP服务端口 | 4009 |
| RAG_DIALOGUE_SERVICE_PORT | 微服务端口 | 3009 |
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 3306 |
| DB_DATABASE | 数据库名称 | rag_dialogue |
| REDIS_HOST | Redis主机 | localhost |
| REDIS_PORT | Redis端口 | 6379 |
| OPENAI_API_KEY | OpenAI API密钥 | - |
| KNOWLEDGE_BASE_SERVICE_URL | 知识库服务地址 | - |

## 🏗️ 项目结构

```
src/
├── app.module.ts              # 主应用模块
├── main.ts                    # 应用入口
├── dialogue/                  # 对话模块
│   ├── dialogue.service.ts    # 对话服务
│   ├── dialogue.controller.ts # 对话控制器
│   ├── dialogue.module.ts     # 对话模块
│   ├── entities/              # 数据实体
│   └── dto/                   # 数据传输对象
├── intent/                    # 意图分析模块
│   ├── intent.service.ts      # 意图服务
│   ├── intent.controller.ts   # 意图控制器
│   ├── intent.module.ts       # 意图模块
│   └── dto/                   # 数据传输对象
├── emotion/                   # 情感分析模块
│   ├── emotion.service.ts     # 情感服务
│   ├── emotion.controller.ts  # 情感控制器
│   ├── emotion.module.ts      # 情感模块
│   └── dto/                   # 数据传输对象
└── health/                    # 健康检查模块
    ├── health.controller.ts   # 健康检查控制器
    ├── health.service.ts      # 健康检查服务
    └── health.module.ts       # 健康检查模块
```

## 🧪 测试

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

## 📊 监控

### 健康检查端点
- `/health` - 基础健康检查
- `/health/detailed` - 详细健康信息
- `/health/readiness` - 就绪检查
- `/health/liveness` - 存活检查

### 日志
服务使用NestJS内置日志系统，支持不同级别的日志输出。

## 🔒 安全

- 输入验证和清理
- SQL注入防护
- XSS防护
- CORS配置
- 速率限制

## 🚀 部署

### Docker部署
```bash
# 构建镜像
docker build -t rag-dialogue-service .

# 运行容器
docker run -p 4009:4009 -p 3009:3009 rag-dialogue-service
```

### Kubernetes部署
参考 `k8s/` 目录下的配置文件。

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 基础对话功能
- 意图理解和情感分析
- 知识检索集成
- 健康检查和监控

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

MIT License
