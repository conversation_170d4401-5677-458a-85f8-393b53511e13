import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { IntentService, IntentResult } from './intent.service';
import { AnalyzeIntentDto } from './dto/analyze-intent.dto';
import { BatchAnalyzeIntentDto } from './dto/batch-analyze-intent.dto';
import { IntentAnalysisResponseDto } from './dto/intent-analysis-response.dto';
import { BatchIntentAnalysisResponseDto } from './dto/batch-intent-analysis-response.dto';
import { IntentStatisticsResponseDto } from './dto/intent-statistics-response.dto';

@ApiTags('intent')
@Controller('intent')
export class IntentController {
  private readonly logger = new Logger(IntentController.name);

  constructor(private readonly intentService: IntentService) {}

  @Post('analyze')
  @ApiOperation({ summary: '分析文本意图' })
  @ApiBody({ type: AnalyzeIntentDto })
  @ApiResponse({
    status: 200,
    description: '意图分析成功',
    type: IntentAnalysisResponseDto,
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async analyzeIntent(
    @Body(ValidationPipe) analyzeDto: AnalyzeIntentDto,
  ): Promise<IntentAnalysisResponseDto> {
    try {
      this.logger.log(`分析意图: ${analyzeDto.text}`);
      
      const result = await this.intentService.analyzeIntent(analyzeDto.text);
      
      return {
        success: true,
        data: result,
        message: '意图分析成功',
      };
    } catch (error) {
      this.logger.error(`意图分析失败: ${error.message}`, error.stack);
      throw new HttpException(
        `意图分析失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('batch-analyze')
  @ApiOperation({ summary: '批量分析文本意图' })
  @ApiBody({ type: BatchAnalyzeIntentDto })
  @ApiResponse({
    status: 200,
    description: '批量意图分析成功',
    type: BatchIntentAnalysisResponseDto,
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async batchAnalyzeIntent(
    @Body(ValidationPipe) batchDto: BatchAnalyzeIntentDto,
  ): Promise<BatchIntentAnalysisResponseDto> {
    try {
      this.logger.log(`批量分析意图: ${batchDto.texts.length} 条文本`);
      
      const results = await this.intentService.batchAnalyzeIntent(batchDto.texts);
      
      return {
        success: true,
        data: {
          results,
          total: results.length,
        },
        message: '批量意图分析成功',
      };
    } catch (error) {
      this.logger.error(`批量意图分析失败: ${error.message}`, error.stack);
      throw new HttpException(
        `批量意图分析失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取意图分析统计信息' })
  @ApiResponse({
    status: 200,
    description: '获取统计信息成功',
    type: IntentStatisticsResponseDto,
  })
  async getStatistics(): Promise<IntentStatisticsResponseDto> {
    try {
      this.logger.log('获取意图分析统计信息');
      
      const statistics = this.intentService.getIntentStatistics();
      
      return {
        success: true,
        data: statistics,
        message: '获取统计信息成功',
      };
    } catch (error) {
      this.logger.error(`获取统计信息失败: ${error.message}`, error.stack);
      throw new HttpException(
        `获取统计信息失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
